const Asset = require('../model/asset');
const User = require('../model/user');
const ExpressError = require('@utils/ExpressError');
const { client } = require('../cache');
const { extractAssetData, deleteFromCloudinary, getFileType } = require('../utils/fileUpload');

// Upload asset with file
exports.uploadAssetWithFile = async (req, res) => {
    try {
        if (!req.file) {
            throw new ExpressError('No file uploaded', 400);
        }

        const { category, description, relatedTo, visibility = 'private', tags, metadata } = req.body;

        // Parse relatedTo if it's a string
        let parsedRelatedTo;
        try {
            parsedRelatedTo = typeof relatedTo === 'string' ? JSON.parse(relatedTo) : relatedTo;
        } catch (error) {
            throw new ExpressError('Invalid relatedTo format', 400);
        }

        // Extract asset data from uploaded file
        const assetData = extractAssetData(req.file, category, parsedRelatedTo);

        // Create new asset
        const asset = new Asset({
            ...assetData,
            description,
            uploadedBy: req.user.id,
            visibility,
            tags: tags ? (typeof tags === 'string' ? JSON.parse(tags) : tags) : [],
            metadata: metadata ? (typeof metadata === 'string' ? JSON.parse(metadata) : metadata) : {}
        });

        await asset.save();

        // Invalidate related cache
        const cacheKey = `assets:${req.user.id}:${category}`;
        await client.del(cacheKey);

        res.status(201).json({
            message: 'Asset uploaded successfully',
            asset: {
                id: asset._id,
                filename: asset.filename,
                originalName: asset.originalName,
                fileType: asset.fileType,
                category: asset.category,
                cloudinaryUrl: asset.cloudinaryUrl,
                thumbnailUrl: asset.thumbnailUrl,
                fileSizeFormatted: asset.fileSizeFormatted,
                createdAt: asset.createdAt
            }
        });
    } catch (error) {
        // If asset creation fails, try to delete the uploaded file from Cloudinary
        if (req.file && req.file.public_id) {
            try {
                await deleteFromCloudinary(req.file.public_id, req.file.resource_type);
            } catch (deleteError) {
                console.error('Failed to delete file from Cloudinary:', deleteError);
            }
        }
        throw new ExpressError(`Upload failed: ${error.message}`, 500);
    }
};

// Upload asset (manual data entry)
exports.uploadAsset = async (req, res) => {
    try {
        const {
            filename,
            originalName,
            description,
            fileType,
            mimeType,
            fileSize,
            category,
            cloudinaryUrl,
            cloudinaryPublicId,
            secureUrl,
            dimensions,
            duration,
            relatedTo,
            visibility = 'private',
            tags,
            metadata,
            expiresAt
        } = req.body;

        // Validate required fields
        if (!filename || !originalName || !fileType || !mimeType || !fileSize ||
            !category || !cloudinaryUrl || !cloudinaryPublicId || !relatedTo) {
            throw new ExpressError('Missing required fields', 400);
        }

        // Create new asset
        const asset = new Asset({
            filename,
            originalName,
            description,
            fileType,
            mimeType,
            fileSize,
            category,
            cloudinaryUrl,
            cloudinaryPublicId,
            secureUrl: secureUrl || cloudinaryUrl.replace('http://', 'https://'),
            dimensions,
            duration,
            uploadedBy: req.user.id,
            relatedTo,
            visibility,
            tags: tags || [],
            metadata: metadata || {},
            expiresAt
        });

        await asset.save();

        // Invalidate related cache
        const cacheKey = `assets:${req.user.id}:${category}`;
        await client.del(cacheKey);

        res.status(201).json({
            message: 'Asset uploaded successfully',
            asset: {
                id: asset._id,
                filename: asset.filename,
                originalName: asset.originalName,
                fileType: asset.fileType,
                category: asset.category,
                cloudinaryUrl: asset.cloudinaryUrl,
                thumbnailUrl: asset.thumbnailUrl,
                fileSizeFormatted: asset.fileSizeFormatted,
                createdAt: asset.createdAt
            }
        });
    } catch (error) {
        throw new ExpressError(`Upload failed: ${error.message}`, 500);
    }
};

// Get user's assets
exports.getUserAssets = async (req, res) => {
    try {
        const { category, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

        const cacheKey = `assets:${req.user.id}:${category || 'all'}:${page}:${limit}`;
        const cachedAssets = await client.get(cacheKey);

        if (cachedAssets) {
            return res.json(JSON.parse(cachedAssets));
        }

        // Build query
        const query = {
            uploadedBy: req.user.id,
            status: 'active'
        };

        if (category) {
            query.category = category;
        }

        // Pagination options
        const options = {
            limit: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit),
            sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
        };

        const assets = await Asset.find(query, null, options);
        const totalAssets = await Asset.countDocuments(query);

        const response = {
            assets: assets.map(asset => ({
                id: asset._id,
                filename: asset.filename,
                originalName: asset.originalName,
                description: asset.description,
                fileType: asset.fileType,
                category: asset.category,
                cloudinaryUrl: asset.cloudinaryUrl,
                thumbnailUrl: asset.thumbnailUrl,
                fileSizeFormatted: asset.fileSizeFormatted,
                dimensions: asset.dimensions,
                tags: asset.tags,
                isVerified: asset.isVerified,
                createdAt: asset.createdAt,
                updatedAt: asset.updatedAt
            })),
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalAssets / parseInt(limit)),
                totalAssets,
                hasNext: parseInt(page) < Math.ceil(totalAssets / parseInt(limit)),
                hasPrev: parseInt(page) > 1
            }
        };

        // Cache for 30 minutes
        await client.setEx(cacheKey, 1800, JSON.stringify(response));
        res.json(response);
    } catch (error) {
        throw new ExpressError(`Failed to fetch assets: ${error.message}`, 500);
    }
};

// Get asset by ID
exports.getAssetById = async (req, res) => {
    try {
        const { assetId } = req.params;

        const asset = await Asset.findById(assetId)
            .populate('uploadedBy', 'name email')
            .populate('verifiedBy', 'name email');

        if (!asset) {
            throw new ExpressError('Asset not found', 404);
        }

        // Check access permissions
        if (!asset.hasAccess(req.user.id)) {
            throw new ExpressError('Access denied', 403);
        }

        res.json({
            id: asset._id,
            filename: asset.filename,
            originalName: asset.originalName,
            description: asset.description,
            fileType: asset.fileType,
            mimeType: asset.mimeType,
            fileSize: asset.fileSize,
            fileSizeFormatted: asset.fileSizeFormatted,
            category: asset.category,
            cloudinaryUrl: asset.cloudinaryUrl,
            secureUrl: asset.secureUrl,
            thumbnailUrl: asset.thumbnailUrl,
            dimensions: asset.dimensions,
            duration: asset.duration,
            uploadedBy: asset.uploadedBy,
            relatedTo: asset.relatedTo,
            visibility: asset.visibility,
            status: asset.status,
            isVerified: asset.isVerified,
            verifiedBy: asset.verifiedBy,
            verifiedAt: asset.verifiedAt,
            tags: asset.tags,
            metadata: asset.metadata,
            createdAt: asset.createdAt,
            updatedAt: asset.updatedAt
        });
    } catch (error) {
        throw new ExpressError(`Failed to fetch asset: ${error.message}`, 500);
    }
};

// Update asset
exports.updateAsset = async (req, res) => {
    try {
        const { assetId } = req.params;
        const { description, tags, visibility, metadata } = req.body;

        const asset = await Asset.findById(assetId);
        if (!asset) {
            throw new ExpressError('Asset not found', 404);
        }

        // Check if user owns the asset
        if (asset.uploadedBy.toString() !== req.user.id) {
            throw new ExpressError('Access denied', 403);
        }

        // Update allowed fields
        if (description !== undefined) asset.description = description;
        if (tags !== undefined) asset.tags = tags;
        if (visibility !== undefined) asset.visibility = visibility;
        if (metadata !== undefined) asset.metadata = { ...asset.metadata, ...metadata };

        await asset.save();

        // Invalidate cache
        const cacheKey = `assets:${req.user.id}:${asset.category}`;
        await client.del(cacheKey);

        res.json({
            message: 'Asset updated successfully',
            asset: {
                id: asset._id,
                description: asset.description,
                tags: asset.tags,
                visibility: asset.visibility,
                metadata: asset.metadata,
                updatedAt: asset.updatedAt
            }
        });
    } catch (error) {
        throw new ExpressError(`Update failed: ${error.message}`, 500);
    }
};

// Delete asset
exports.deleteAsset = async (req, res) => {
    try {
        const { assetId } = req.params;
        const { permanent = false } = req.query;

        const asset = await Asset.findById(assetId);
        if (!asset) {
            throw new ExpressError('Asset not found', 404);
        }

        // Check if user owns the asset or is admin
        if (asset.uploadedBy.toString() !== req.user.id && req.user.role !== 'admin') {
            throw new ExpressError('Access denied', 403);
        }

        if (permanent === 'true' || permanent === true) {
            // Permanent delete - remove from Cloudinary and database
            try {
                // Determine resource type for Cloudinary deletion
                let resourceType = 'auto';
                if (asset.fileType === 'video') resourceType = 'video';
                else if (asset.fileType === 'image') resourceType = 'image';
                else resourceType = 'raw';

                await deleteFromCloudinary(asset.cloudinaryPublicId, resourceType);
            } catch (cloudinaryError) {
                console.error('Failed to delete from Cloudinary:', cloudinaryError);
                // Continue with database deletion even if Cloudinary deletion fails
            }

            // Delete from database
            await Asset.findByIdAndDelete(assetId);
        } else {
            // Soft delete by updating status
            asset.status = 'deleted';
            await asset.save();
        }

        // Invalidate cache
        const cacheKey = `assets:${req.user.id}:${asset.category}`;
        await client.del(cacheKey);

        res.json({
            message: permanent ? 'Asset permanently deleted successfully' : 'Asset deleted successfully'
        });
    } catch (error) {
        throw new ExpressError(`Delete failed: ${error.message}`, 500);
    }
};

// Get assets by category
exports.getAssetsByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const { page = 1, limit = 20 } = req.query;

        const cacheKey = `assets:category:${category}:${page}:${limit}`;
        const cachedAssets = await client.get(cacheKey);

        if (cachedAssets) {
            return res.json(JSON.parse(cachedAssets));
        }

        const options = {
            limit: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit),
            sort: { createdAt: -1 }
        };

        const assets = await Asset.getByCategory(category, req.user.id, options);
        const totalAssets = await Asset.countDocuments({
            category,
            status: 'active',
            $or: [
                { uploadedBy: req.user.id },
                { visibility: 'public' },
                { visibility: 'restricted', accessibleBy: req.user.id }
            ]
        });

        const response = {
            category,
            assets: assets.map(asset => ({
                id: asset._id,
                filename: asset.filename,
                originalName: asset.originalName,
                fileType: asset.fileType,
                cloudinaryUrl: asset.cloudinaryUrl,
                thumbnailUrl: asset.thumbnailUrl,
                uploadedBy: asset.uploadedBy,
                createdAt: asset.createdAt
            })),
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalAssets / parseInt(limit)),
                totalAssets
            }
        };

        // Cache for 15 minutes
        await client.setEx(cacheKey, 900, JSON.stringify(response));
        res.json(response);
    } catch (error) {
        throw new ExpressError(`Failed to fetch assets: ${error.message}`, 500);
    }
};
