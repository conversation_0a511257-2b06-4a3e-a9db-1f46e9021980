const ExpressError = require('@utils/ExpressError');
const User = require('../model/user');
const ServiceRequest = require('../model/serviceRequest');
const Booking = require('../model/booking');
const { client } = require('../cache');

// Register a new contractor (includes location)
exports.contractorRegister = async (req, res) => {
    const { email, location, documents, specialties, portfolio, experience } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
        throw new ExpressError('User not found. Please sign up as a user first', 404);
    }

    if (user.role !== 'user') {
        throw new ExpressError('User already registered with other role', 400);
    }

    // Update user to contractor role and add contractor-specific fields
    user.role = 'contractor';
    user.location = location;
    user.documents = documents || [];
    user.specialties = specialties || [];
    user.portfolio = portfolio || [];
    user.experience = experience || 0;

    try {
        await user.save();
        res.status(200).json({
            message: 'Successfully registered as a contractor',
        });
    } catch (error) {
        if (
            error.code === 11000 &&
            error.keyPattern &&
            error.keyPattern.email
        ) {
            throw new ExpressError('User already exists', 400);
        }
        throw new ExpressError(`Failed to register contractor: ${error.message}`, 500);
    }
};

// Get contractor profile
exports.getContractorProfile = async (req, res) => {
    try {
        const cacheKey = `contractor:${req.user.id}`;
        const cachedProfile = await client.get(cacheKey);

        if (cachedProfile) {
            return res.json(JSON.parse(cachedProfile));
        }

        const contractor = await User.findById(req.user.id).populate('verifiedByBroker');
        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        // Calculate average rating
        const averageRating = contractor.ratings.length > 0
            ? contractor.ratings.reduce((sum, rating) => sum + rating, 0) / contractor.ratings.length
            : 0;

        const profile = {
            id: contractor._id,
            name: contractor.name,
            email: contractor.email,
            phone: contractor.phone,
            role: contractor.role,
            location: contractor.location,
            documents: contractor.documents,
            specialties: contractor.specialties,
            portfolio: contractor.portfolio,
            experience: contractor.experience,
            verificationStatus: contractor.verificationStatus,
            verifiedByBroker: contractor.verifiedByBroker,
            approvalDate: contractor.approvalDate,
            ratings: contractor.ratings,
            averageRating: parseFloat(averageRating.toFixed(2)),
            totalProjects: contractor.totalProjects,
            createdAt: contractor.createdAt,
            updatedAt: contractor.updatedAt,
        };

        await client.setEx(cacheKey, 3600, JSON.stringify(profile));
        res.json(profile);
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch profile: ${error.message}`,
            500
        );
    }
};

// Update contractor profile (updates MongoDB, invalidates Redis cache)
exports.updateContractorProfile = async (req, res) => {
    const { location, documents, specialties, portfolio, experience } = req.body;

    // Build update object with only provided fields
    const updateFields = {};
    if (location !== undefined) updateFields.location = location;
    if (documents !== undefined) updateFields.documents = documents;
    if (specialties !== undefined) updateFields.specialties = specialties;
    if (portfolio !== undefined) updateFields.portfolio = portfolio;
    if (experience !== undefined) updateFields.experience = experience;

    try {
        const contractor = await User.findByIdAndUpdate(
            req.user.id,
            updateFields,
            { new: true, runValidators: true }
        ).populate('verifiedByBroker');

        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        const cacheKey = `contractor:${req.user.id}`;
        await client.del(cacheKey);

        // Calculate average rating
        const averageRating = contractor.ratings.length > 0
            ? contractor.ratings.reduce((sum, rating) => sum + rating, 0) / contractor.ratings.length
            : 0;

        const profile = {
            id: contractor._id,
            name: contractor.name,
            email: contractor.email,
            phone: contractor.phone,
            role: contractor.role,
            location: contractor.location,
            documents: contractor.documents,
            specialties: contractor.specialties,
            portfolio: contractor.portfolio,
            experience: contractor.experience,
            verificationStatus: contractor.verificationStatus,
            verifiedByBroker: contractor.verifiedByBroker,
            approvalDate: contractor.approvalDate,
            ratings: contractor.ratings,
            averageRating: parseFloat(averageRating.toFixed(2)),
            totalProjects: contractor.totalProjects,
            createdAt: contractor.createdAt,
            updatedAt: contractor.updatedAt,
        };

        res.json({ message: 'Profile updated successfully', profile });
    } catch (error) {
        throw new ExpressError(`Update failed: ${error.message}`, 500);
    }
};

// Get service requests
exports.getServiceRequests = async (req, res) => {
    try {
        const contractor = await User.findById(req.user.id);
        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        const cacheKey = `requests:${req.user.id}`;
        const cachedRequests = await client.get(cacheKey);

        if (cachedRequests) {
            return res.json({ requests: JSON.parse(cachedRequests) });
        }

        const requests = await ServiceRequest.find({
            contractor_id: contractor._id,
        }).populate('user_id');
        await client.setEx(cacheKey, 3600, JSON.stringify(requests)); // Cache for 1 hour in Redis
        res.json({ requests });
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch requests: ${error.message}`,
            500
        );
    }
};

// Accept or decline a project offer
exports.respondToRequest = async (req, res) => {
    const { requestId } = req.params;
    const { status } = req.body; // 'accepted' or 'declined'
    try {
        const contractor = await User.findById(req.user.id);
        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        const request = await ServiceRequest.findOneAndUpdate(
            { _id: requestId, contractor_id: contractor._id },
            { status },
            { new: true }
        ).populate('user_id');
        if (!request) throw new ExpressError('Request not found', 404);

        const cacheKey = `requests:${req.user.id}`;
        await client.del(cacheKey);
        res.json({ message: `Request ${status}`, request });
    } catch (error) {
        throw new ExpressError(
            `Failed to respond to request: ${error.message}`,
            500
        );
    }
};

// Get bookings (fetches from MongoDB)
exports.getBookings = async (req, res) => {
    try {
        const contractor = await User.findById(req.user.id);
        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        const bookings = await Booking.find({
            contractor_id: contractor._id,
        }).populate('user_id');
        res.json({ bookings });
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch bookings: ${error.message}`,
            500
        );
    }
};
