const mongoose = require('mongoose');

const siteSchema = new mongoose.Schema({
    // Basic site information
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    siteType: {
        type: String,
        required: true,
        enum: [
            'residential',
            'commercial',
            'industrial',
            'infrastructure',
            'renovation',
            'new_construction',
            'demolition',
            'landscaping'
        ]
    },
    
    // Location information
    address: {
        street: {
            type: String,
            required: true
        },
        city: {
            type: String,
            required: true
        },
        state: {
            type: String,
            required: true
        },
        zipCode: {
            type: String,
            required: true
        },
        country: {
            type: String,
            default: 'USA'
        },
        coordinates: {
            latitude: Number,
            longitude: Number
        }
    },
    
    // Site details
    size: {
        area: {
            type: Number, // in square feet
            required: true
        },
        unit: {
            type: String,
            enum: ['sqft', 'sqm', 'acres', 'hectares'],
            default: 'sqft'
        }
    },
    
    // Project information
    projectType: {
        type: String,
        required: true,
        enum: [
            'construction',
            'renovation',
            'repair',
            'maintenance',
            'inspection',
            'demolition',
            'landscaping',
            'electrical',
            'plumbing',
            'roofing',
            'flooring',
            'painting',
            'hvac'
        ]
    },
    
    // Timeline
    timeline: {
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date,
            required: true
        },
        estimatedDuration: {
            type: Number, // in days
            required: true
        }
    },
    
    // Budget information
    budget: {
        estimated: {
            type: Number,
            required: true
        },
        actual: {
            type: Number,
            default: 0
        },
        currency: {
            type: String,
            default: 'USD'
        }
    },
    
    // People involved
    owner: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    broker: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User' // User with role 'broker'
    },
    contractors: [{
        contractor: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User' // User with role 'contractor'
        },
        role: {
            type: String,
            enum: ['primary', 'secondary', 'specialist', 'subcontractor']
        },
        assignedDate: {
            type: Date,
            default: Date.now
        },
        status: {
            type: String,
            enum: ['assigned', 'active', 'completed', 'removed'],
            default: 'assigned'
        }
    }],
    
    // Site status and progress
    status: {
        type: String,
        enum: [
            'planning',
            'approved',
            'in_progress',
            'on_hold',
            'completed',
            'cancelled',
            'inspection',
            'delayed'
        ],
        default: 'planning'
    },
    
    progress: {
        percentage: {
            type: Number,
            min: 0,
            max: 100,
            default: 0
        },
        milestones: [{
            name: String,
            description: String,
            targetDate: Date,
            completedDate: Date,
            status: {
                type: String,
                enum: ['pending', 'in_progress', 'completed', 'delayed'],
                default: 'pending'
            }
        }],
        lastUpdated: {
            type: Date,
            default: Date.now
        }
    },
    
    // Requirements and specifications
    requirements: {
        permits: [{
            type: String,
            number: String,
            issuedDate: Date,
            expiryDate: Date,
            status: {
                type: String,
                enum: ['pending', 'approved', 'expired', 'rejected'],
                default: 'pending'
            }
        }],
        specialRequirements: [String],
        safetyRequirements: [String],
        environmentalConsiderations: [String]
    },
    
    // Assets and documentation
    assets: {
        documents: [{
            type: String // Asset IDs
        }],
        images: [{
            type: String // Asset IDs
        }],
        videos: [{
            type: String // Asset IDs
        }],
        blueprints: [{
            type: String // Asset IDs
        }],
        reports: [{
            type: String // Asset IDs
        }]
    },
    
    // Communication and updates
    updates: [{
        author: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        message: {
            type: String,
            required: true
        },
        type: {
            type: String,
            enum: ['general', 'progress', 'issue', 'milestone', 'safety', 'delay'],
            default: 'general'
        },
        attachments: [String], // Asset IDs
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    // Issues and concerns
    issues: [{
        title: {
            type: String,
            required: true
        },
        description: String,
        severity: {
            type: String,
            enum: ['low', 'medium', 'high', 'critical'],
            default: 'medium'
        },
        category: {
            type: String,
            enum: ['safety', 'quality', 'timeline', 'budget', 'weather', 'equipment', 'personnel'],
            required: true
        },
        reportedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        assignedTo: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        status: {
            type: String,
            enum: ['open', 'in_progress', 'resolved', 'closed'],
            default: 'open'
        },
        priority: {
            type: String,
            enum: ['low', 'medium', 'high', 'urgent'],
            default: 'medium'
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        resolvedAt: Date,
        resolution: String
    }],
    
    // Site settings and preferences
    settings: {
        isPublic: {
            type: Boolean,
            default: false
        },
        allowPublicUpdates: {
            type: Boolean,
            default: false
        },
        notificationSettings: {
            emailUpdates: {
                type: Boolean,
                default: true
            },
            smsAlerts: {
                type: Boolean,
                default: false
            },
            milestoneNotifications: {
                type: Boolean,
                default: true
            }
        }
    },
    
    // Metadata
    tags: [String],
    notes: String,
    
    // Archive information
    isArchived: {
        type: Boolean,
        default: false
    },
    archivedAt: Date,
    archivedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

// Indexes for better performance
siteSchema.index({ owner: 1 });
siteSchema.index({ broker: 1 });
siteSchema.index({ 'contractors.contractor': 1 });
siteSchema.index({ status: 1 });
siteSchema.index({ siteType: 1 });
siteSchema.index({ projectType: 1 });
siteSchema.index({ 'address.city': 1, 'address.state': 1 });
siteSchema.index({ 'timeline.startDate': 1, 'timeline.endDate': 1 });
siteSchema.index({ createdAt: -1 });
siteSchema.index({ isArchived: 1 });

// Virtual for full address
siteSchema.virtual('fullAddress').get(function() {
    const { street, city, state, zipCode, country } = this.address;
    return [street, city, state, zipCode, country].filter(Boolean).join(', ');
});

// Virtual for project duration in days
siteSchema.virtual('actualDuration').get(function() {
    if (this.timeline.startDate && this.timeline.endDate) {
        const diffTime = Math.abs(this.timeline.endDate - this.timeline.startDate);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    return 0;
});

// Virtual for budget variance
siteSchema.virtual('budgetVariance').get(function() {
    return this.budget.actual - this.budget.estimated;
});

// Virtual for budget variance percentage
siteSchema.virtual('budgetVariancePercentage').get(function() {
    if (this.budget.estimated === 0) return 0;
    return ((this.budget.actual - this.budget.estimated) / this.budget.estimated) * 100;
});

// Method to check if user has access to site
siteSchema.methods.hasAccess = function(userId) {
    // Owner always has access
    if (this.owner.toString() === userId.toString()) {
        return true;
    }
    
    // Broker has access
    if (this.broker && this.broker.toString() === userId.toString()) {
        return true;
    }
    
    // Assigned contractors have access
    const isContractor = this.contractors.some(c => 
        c.contractor.toString() === userId.toString() && c.status === 'active'
    );
    
    return isContractor;
};

// Method to add contractor to site
siteSchema.methods.addContractor = function(contractorId, role = 'secondary') {
    // Check if contractor is already assigned
    const existingContractor = this.contractors.find(c => 
        c.contractor.toString() === contractorId.toString()
    );
    
    if (existingContractor) {
        existingContractor.status = 'active';
        existingContractor.role = role;
        existingContractor.assignedDate = new Date();
    } else {
        this.contractors.push({
            contractor: contractorId,
            role: role,
            status: 'assigned'
        });
    }
};

// Method to remove contractor from site
siteSchema.methods.removeContractor = function(contractorId) {
    const contractor = this.contractors.find(c => 
        c.contractor.toString() === contractorId.toString()
    );
    
    if (contractor) {
        contractor.status = 'removed';
    }
};

// Method to update progress
siteSchema.methods.updateProgress = function(percentage, milestoneUpdate = null) {
    this.progress.percentage = Math.max(0, Math.min(100, percentage));
    this.progress.lastUpdated = new Date();
    
    if (milestoneUpdate) {
        const milestone = this.progress.milestones.id(milestoneUpdate.milestoneId);
        if (milestone) {
            milestone.status = milestoneUpdate.status;
            if (milestoneUpdate.status === 'completed') {
                milestone.completedDate = new Date();
            }
        }
    }
    
    // Auto-update site status based on progress
    if (percentage === 100) {
        this.status = 'completed';
    } else if (percentage > 0 && this.status === 'planning') {
        this.status = 'in_progress';
    }
};

// Static method to find sites by location
siteSchema.statics.findByLocation = function(city, state, radius = 50) {
    return this.find({
        'address.city': new RegExp(city, 'i'),
        'address.state': new RegExp(state, 'i'),
        isArchived: false
    });
};

// Static method to find sites by contractor
siteSchema.statics.findByContractor = function(contractorId) {
    return this.find({
        'contractors.contractor': contractorId,
        'contractors.status': { $in: ['assigned', 'active'] },
        isArchived: false
    });
};

// Pre-save middleware
siteSchema.pre('save', function(next) {
    // Calculate estimated duration if not provided
    if (!this.timeline.estimatedDuration && this.timeline.startDate && this.timeline.endDate) {
        const diffTime = Math.abs(this.timeline.endDate - this.timeline.startDate);
        this.timeline.estimatedDuration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    
    // Update progress lastUpdated when progress changes
    if (this.isModified('progress.percentage')) {
        this.progress.lastUpdated = new Date();
    }
    
    next();
});

const Site = mongoose.model('Site', siteSchema);
module.exports = Site;
