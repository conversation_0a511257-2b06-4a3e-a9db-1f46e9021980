# Assets Management API Documentation

## Overview
The Assets Management system provides comprehensive file upload, storage, and management capabilities for the Build Connect platform. It supports various file types including images, videos, documents, and audio files with cloud storage integration via Cloudinary.

## Features
- **Multi-format Support**: Images, videos, documents, audio files
- **Cloud Storage**: Cloudinary integration with automatic optimization
- **Access Control**: Public, private, and restricted access levels
- **Categorization**: Organized by purpose (profile pictures, portfolio, documents, etc.)
- **Caching**: Redis-based caching for improved performance
- **Metadata**: Rich metadata support with custom fields
- **Verification**: Document verification workflow
- **Soft Delete**: Safe deletion with recovery options

## Asset Categories

### Available Categories
- `profile_picture` - User profile images
- `portfolio_item` - Portfolio showcase items
- `document` - General documents
- `project_file` - Project-related files
- `verification_document` - Identity/business verification docs
- `license_document` - Professional licenses
- `certificate` - Certifications and awards
- `before_after_photo` - Project progress photos
- `progress_photo` - Work in progress images
- `other` - Miscellaneous files

## API Endpoints

### Upload Endpoints

#### Upload Profile Picture
```
POST /api/assets/upload/profile-picture
Content-Type: multipart/form-data

Body:
- file: Image file (JPEG, PNG, GIF, WebP)
- description: Optional description
- relatedTo: JSON string {"entityType": "user", "entityId": "userId"}
- visibility: "public" | "private" | "restricted" (default: "private")
- tags: JSON array of strings (optional)
- metadata: JSON object (optional)
```

#### Upload Portfolio Item
```
POST /api/assets/upload/portfolio
Content-Type: multipart/form-data

Body:
- file: Image or video file
- description: Portfolio item description
- relatedTo: JSON string {"entityType": "portfolio", "entityId": "portfolioId"}
- visibility: "public" | "private" | "restricted"
- tags: JSON array ["construction", "renovation", etc.]
```

#### Upload Document
```
POST /api/assets/upload/document
Content-Type: multipart/form-data

Body:
- file: Document file (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT)
- description: Document description
- relatedTo: JSON string {"entityType": "user", "entityId": "userId"}
- visibility: "private" (recommended for documents)
```

#### Upload Project File
```
POST /api/assets/upload/project-file
Content-Type: multipart/form-data

Body:
- file: Any supported file type
- description: File description
- relatedTo: JSON string {"entityType": "project", "entityId": "projectId"}
- tags: JSON array ["blueprint", "contract", "photo", etc.]
```

### Retrieval Endpoints

#### Get User's Assets
```
GET /api/assets/my-assets?category=portfolio&page=1&limit=20&sortBy=createdAt&sortOrder=desc

Query Parameters:
- category: Filter by asset category (optional)
- page: Page number (default: 1)
- limit: Items per page (default: 20, max: 100)
- sortBy: Sort field (createdAt, filename, fileSize)
- sortOrder: asc | desc (default: desc)

Response:
{
  "assets": [
    {
      "id": "asset_id",
      "filename": "processed_filename",
      "originalName": "original_filename.jpg",
      "description": "Asset description",
      "fileType": "image",
      "category": "portfolio_item",
      "cloudinaryUrl": "https://res.cloudinary.com/...",
      "thumbnailUrl": "https://res.cloudinary.com/.../w_300,h_300,c_fill/...",
      "fileSizeFormatted": "2.5 MB",
      "dimensions": {"width": 1920, "height": 1080},
      "tags": ["renovation", "kitchen"],
      "isVerified": false,
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalAssets": 95,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### Get Asset by ID
```
GET /api/assets/:assetId

Response:
{
  "id": "asset_id",
  "filename": "processed_filename",
  "originalName": "original_filename.jpg",
  "description": "Asset description",
  "fileType": "image",
  "mimeType": "image/jpeg",
  "fileSize": 2621440,
  "fileSizeFormatted": "2.5 MB",
  "category": "portfolio_item",
  "cloudinaryUrl": "https://res.cloudinary.com/...",
  "secureUrl": "https://res.cloudinary.com/...",
  "thumbnailUrl": "https://res.cloudinary.com/.../w_300,h_300,c_fill/...",
  "dimensions": {"width": 1920, "height": 1080},
  "duration": null,
  "uploadedBy": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "relatedTo": {
    "entityType": "portfolio",
    "entityId": "portfolio_id"
  },
  "visibility": "public",
  "status": "active",
  "isVerified": false,
  "verifiedBy": null,
  "verifiedAt": null,
  "tags": ["renovation", "kitchen"],
  "metadata": {"project": "Kitchen Renovation 2024"},
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

#### Get Assets by Category
```
GET /api/assets/category/:category?page=1&limit=20

Response: Similar to getUserAssets but filtered by category
```

### Management Endpoints

#### Update Asset
```
PUT /api/assets/:assetId
Content-Type: application/json

Body:
{
  "description": "Updated description",
  "tags": ["new", "tags"],
  "visibility": "public",
  "metadata": {"additionalInfo": "value"}
}

Response:
{
  "message": "Asset updated successfully",
  "asset": {
    "id": "asset_id",
    "description": "Updated description",
    "tags": ["new", "tags"],
    "visibility": "public",
    "metadata": {"additionalInfo": "value"},
    "updatedAt": "2024-01-15T11:00:00Z"
  }
}
```

#### Delete Asset
```
DELETE /api/assets/:assetId?permanent=false

Query Parameters:
- permanent: true | false (default: false)
  - false: Soft delete (status = 'deleted')
  - true: Permanent delete (removes from Cloudinary and database)

Response:
{
  "message": "Asset deleted successfully"
}
```

## File Type Support

### Images
- **Formats**: JPEG, JPG, PNG, GIF, WebP
- **Max Size**: 10MB
- **Features**: Automatic optimization, thumbnail generation, responsive transformations

### Videos
- **Formats**: MP4, AVI, MOV, WMV, WebM
- **Max Size**: 100MB
- **Features**: Automatic compression, thumbnail extraction

### Documents
- **Formats**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT
- **Max Size**: 25MB
- **Features**: Secure storage, access control

### Audio
- **Formats**: MP3, WAV, OGG
- **Max Size**: 50MB
- **Features**: Duration extraction, metadata parsing

## Error Handling

### Common Error Responses
```json
{
  "message": "Error description",
  "statusCode": 400
}
```

### Error Codes
- `400` - Bad Request (missing fields, invalid format)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (access denied)
- `404` - Not Found (asset not found)
- `413` - Payload Too Large (file size exceeded)
- `415` - Unsupported Media Type (invalid file type)
- `500` - Internal Server Error

## Usage Examples

### Upload a Profile Picture
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('description', 'My profile picture');
formData.append('relatedTo', JSON.stringify({
  entityType: 'user',
  entityId: userId
}));
formData.append('visibility', 'public');

const response = await fetch('/api/assets/upload/profile-picture', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Session': sessionId
  },
  body: formData
});
```

### Get Portfolio Items
```javascript
const response = await fetch('/api/assets/my-assets?category=portfolio_item&page=1&limit=12', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Session': sessionId
  }
});

const data = await response.json();
console.log(data.assets); // Array of portfolio assets
```

## Security Considerations

1. **Authentication**: All endpoints require valid JWT token
2. **Authorization**: Users can only access their own assets or public assets
3. **File Validation**: Strict file type and size validation
4. **Secure URLs**: HTTPS-only Cloudinary URLs
5. **Access Control**: Granular visibility settings
6. **Soft Delete**: Safe deletion with recovery options

## Performance Features

1. **Redis Caching**: Frequently accessed asset lists are cached
2. **CDN Delivery**: Cloudinary's global CDN for fast delivery
3. **Image Optimization**: Automatic format and quality optimization
4. **Lazy Loading**: Thumbnail URLs for efficient loading
5. **Pagination**: Efficient data retrieval with pagination
