const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const path = require('path');

// Configure Cloudinary (make sure to set these environment variables)
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// File type validation
const allowedFileTypes = {
    image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    video: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'],
    document: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain'
    ],
    audio: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3']
};

// Get file type based on mime type
const getFileType = (mimeType) => {
    for (const [type, mimes] of Object.entries(allowedFileTypes)) {
        if (mimes.includes(mimeType)) {
            return type;
        }
    }
    return 'other';
};

// Validate file type
const validateFileType = (file, allowedTypes = ['image', 'video', 'document', 'audio']) => {
    const fileType = getFileType(file.mimetype);
    return allowedTypes.includes(fileType);
};

// Generate folder path based on category and user
const generateFolderPath = (userId, category) => {
    const baseFolder = 'build-connect';
    return `${baseFolder}/${userId}/${category}`;
};

// Generate public ID for the file
const generatePublicId = (req, file) => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(file.originalname).toLowerCase();
    const baseName = path.basename(file.originalname, extension);
    
    return `${baseName}_${timestamp}_${randomString}`;
};

// Cloudinary storage configuration
const createCloudinaryStorage = (category, allowedTypes = ['image', 'video', 'document', 'audio']) => {
    return new CloudinaryStorage({
        cloudinary: cloudinary,
        params: async (req, file) => {
            // Validate file type
            if (!validateFileType(file, allowedTypes)) {
                throw new Error(`File type ${file.mimetype} is not allowed for category ${category}`);
            }

            const fileType = getFileType(file.mimetype);
            const folder = generateFolderPath(req.user.id, category);
            const publicId = generatePublicId(req, file);

            // Set resource type based on file type
            let resourceType = 'auto';
            if (fileType === 'video') resourceType = 'video';
            else if (fileType === 'image') resourceType = 'image';
            else resourceType = 'raw'; // For documents and other files

            return {
                folder: folder,
                public_id: publicId,
                resource_type: resourceType,
                // Add transformations for images
                ...(fileType === 'image' && {
                    transformation: [
                        { quality: 'auto:good' },
                        { fetch_format: 'auto' }
                    ]
                }),
                // Add tags for better organization
                tags: [category, fileType, `user_${req.user.id}`]
            };
        }
    });
};

// File size limits (in bytes)
const fileSizeLimits = {
    image: 10 * 1024 * 1024,      // 10MB
    video: 100 * 1024 * 1024,     // 100MB
    document: 25 * 1024 * 1024,   // 25MB
    audio: 50 * 1024 * 1024,      // 50MB
    other: 25 * 1024 * 1024       // 25MB
};

// Create multer upload middleware
const createUploadMiddleware = (category, allowedTypes = ['image', 'video', 'document', 'audio']) => {
    const storage = createCloudinaryStorage(category, allowedTypes);
    
    return multer({
        storage: storage,
        limits: {
            fileSize: Math.max(...Object.values(fileSizeLimits)) // Use the largest limit
        },
        fileFilter: (req, file, cb) => {
            try {
                // Validate file type
                if (!validateFileType(file, allowedTypes)) {
                    return cb(new Error(`File type ${file.mimetype} is not allowed`), false);
                }

                // Check file size based on type
                const fileType = getFileType(file.mimetype);
                const maxSize = fileSizeLimits[fileType] || fileSizeLimits.other;
                
                // Note: file.size is not available in fileFilter, so we'll check it later
                cb(null, true);
            } catch (error) {
                cb(error, false);
            }
        }
    });
};

// Specific upload middlewares for different categories
const uploadMiddlewares = {
    profilePicture: createUploadMiddleware('profile_pictures', ['image']),
    portfolioItem: createUploadMiddleware('portfolio', ['image', 'video']),
    document: createUploadMiddleware('documents', ['document']),
    projectFile: createUploadMiddleware('projects', ['image', 'video', 'document']),
    verificationDocument: createUploadMiddleware('verification', ['document', 'image']),
    certificate: createUploadMiddleware('certificates', ['document', 'image']),
    progressPhoto: createUploadMiddleware('progress', ['image', 'video'])
};

// Helper function to extract asset data from uploaded file
const extractAssetData = (file, category, relatedTo) => {
    const fileType = getFileType(file.mimetype);
    
    return {
        filename: file.filename,
        originalName: file.originalname,
        fileType: fileType,
        mimeType: file.mimetype,
        fileSize: file.size,
        category: category,
        cloudinaryUrl: file.path,
        cloudinaryPublicId: file.public_id,
        secureUrl: file.secure_url || file.path.replace('http://', 'https://'),
        dimensions: file.width && file.height ? {
            width: file.width,
            height: file.height
        } : undefined,
        duration: file.duration,
        relatedTo: relatedTo
    };
};

// Delete file from Cloudinary
const deleteFromCloudinary = async (publicId, resourceType = 'auto') => {
    try {
        const result = await cloudinary.uploader.destroy(publicId, {
            resource_type: resourceType
        });
        return result;
    } catch (error) {
        console.error('Error deleting from Cloudinary:', error);
        throw error;
    }
};

// Generate transformation URL
const generateTransformationUrl = (publicId, transformations) => {
    return cloudinary.url(publicId, {
        transformation: transformations
    });
};

module.exports = {
    cloudinary,
    uploadMiddlewares,
    createUploadMiddleware,
    extractAssetData,
    deleteFromCloudinary,
    generateTransformationUrl,
    getFileType,
    validateFileType,
    fileSizeLimits,
    allowedFileTypes
};
