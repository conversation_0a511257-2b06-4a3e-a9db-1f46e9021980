const express = require('express');
const router = express.Router();
const assetController = require('../controllers/asset');
const { catchAsync } = require('@utils');
const doAuthenticate = require('../middleware/authmiddleware');
const { uploadMiddlewares } = require('../utils/fileUpload');

// All routes require authentication
router.use(doAuthenticate);

// Upload asset with file (different endpoints for different categories)
router.post('/upload/profile-picture',
    uploadMiddlewares.profilePicture.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/portfolio',
    uploadMiddlewares.portfolioItem.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/document',
    uploadMiddlewares.document.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/project-file',
    uploadMiddlewares.projectFile.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/verification',
    uploadMiddlewares.verificationDocument.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/certificate',
    uploadMiddlewares.certificate.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

router.post('/upload/progress-photo',
    uploadMiddlewares.progressPhoto.single('file'),
    catchAsync(assetController.uploadAssetWithFile)
);

// Upload asset (manual data entry)
router.post('/upload', catchAsync(assetController.uploadAsset));

// Get user's assets with optional filtering
router.get('/my-assets', catchAsync(assetController.getUserAssets));

// Get assets by category
router.get('/category/:category', catchAsync(assetController.getAssetsByCategory));

// Get specific asset by ID
router.get('/:assetId', catchAsync(assetController.getAssetById));

// Update asset
router.put('/:assetId', catchAsync(assetController.updateAsset));

// Delete asset
router.delete('/:assetId', catchAsync(assetController.deleteAsset));

module.exports = router;
