1.1.0 / 2019-04-24
==================

  * Add `test(string)` function

1.0.2 / 2019-04-19
==================

  * Fix JSDoc comment for `parse` function

1.0.1 / 2018-10-20
==================

  * Remove left over `parameters` property from class

1.0.0 / 2018-10-20
==================

This major release brings the module back to it's RFC 6838 roots. If you want
a module to parse the `Content-Type` or similar HTTP headers, use the
`content-type` module instead.

  * Drop support for Node.js below 0.8
  * Remove parameter handling, which is outside RFC 6838 scope
  * Remove `parse(req)` and `parse(res)` signatures
  * perf: enable strict mode
  * perf: use a class for object creation

0.3.0 / 2014-09-07
==================

  * Support Node.js 0.6
  * Throw error when parameter format invalid on parse

0.2.0 / 2014-06-18
==================

  * Add `typer.format()` to format media types

0.1.0 / 2014-06-17
==================

  * Accept `req` as argument to `parse`
  * Accept `res` as argument to `parse`
  * Parse media type with extra LWS between type and first parameter

0.0.0 / 2014-06-13
==================

  * Initial implementation
