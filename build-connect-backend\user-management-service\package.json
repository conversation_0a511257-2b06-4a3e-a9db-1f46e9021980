{"name": "user-management-service", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@build-connect/utils": "^1.0.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^2.0.0", "redis": "^4.7.0"}}