{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^2.0.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "redis": "^4.7.0"}, "_moduleAliases": {"@utils": "../utils"}}