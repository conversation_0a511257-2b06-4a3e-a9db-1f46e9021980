{"name": "backend", "version": "1.0.0", "private": true, "workspaces": ["user-management-service", "site-management-service", "rating-management-service", "payment-management-service", "notification-management-service", "customer-support-service", "admin-management-service"], "main": ".eslintrc.js", "scripts": {"start": "concurrently \"npm --workspace user-management-service start\" \"npm --workspace site-management-service start\" \"npm --workspace rating-management-service start\" \"npm --workspace payment-management-service start\" \"npm --workspace notification-management-service start\" \"npm --workspace customer-support-service start\" \"npm --workspace admin-management-service start\"", "format": "prettier . --write"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.1.2", "eslint": "^8.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-no-autofix": "^2.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0"}}