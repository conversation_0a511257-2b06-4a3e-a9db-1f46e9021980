const mongoose = require('mongoose');

const contractorSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        unique: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
    },
    password: {
        type: String,
        required: true,
    },
    phone: {
        type: String,
        required: true,
    },
    role: {
        type: String,
        default: 'contractor',
    },
    location: {
        type: String,
        required: true,
    },
    expertise: {
        type: String,
        required: true,
    },
    pricing: {
        type: Object,
        required: true,
    },
    verification_status: {
        type: String,
        default: 'pending',
    },
});

module.exports = mongoose.model('contractor', contractorSchema);
