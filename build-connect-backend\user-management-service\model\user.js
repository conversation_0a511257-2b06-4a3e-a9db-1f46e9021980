const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    // Basic user fields
    name: {
        type: String,
        required: true,
        trim: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    role: {
        type: String,
        enum: ['admin', 'user', 'contractor', 'broker'],
        default: 'user',
    },
    phone: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    password: {
        type: String,
        required: true,
        minlength: 6,
    },

    // Contractor-specific fields (only used when role is 'contractor')
    location: {
        type: String,
        required: function () {
            return this.role === 'contractor';
        }
    },
    documents: {
        type: [String],
        default: []
    },
    specialties: {
        type: [String],
        default: []
    },
    portfolio: {
        type: [String],
        default: []
    },
    experience: {
        type: Number,
        default: 0
    },
    verifiedByBroker: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User' // Reference to User with role 'broker'
    },
    verificationStatus: {
        type: String,
        enum: ['pending', 'verified', 'rejected'],
        default: 'pending'
    },
    approvalDate: {
        type: Date
    },
    ratings: {
        type: [Number],
        default: []
    },
    totalProjects: {
        type: Number,
        default: 0
    },

    // Broker-specific fields (only used when role is 'broker')
    brokerLicense: {
        type: String
    },
    brokerRegion: {
        type: String
    },
    brokerCompany: {
        type: String
    }
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);

module.exports = User;
