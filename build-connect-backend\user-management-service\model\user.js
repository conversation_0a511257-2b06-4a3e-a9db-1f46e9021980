const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    role: {
        type: String,
        enum: ['admin', 'user', 'contractor', 'broker'],
        default: 'user',
    },
    phone: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    password: {
        type: String,
        required: true,
        minlength: 6,
    },
    avatar: {
        type: String,
        default:
            'https://res.cloudinary.com/dyyfuewh4/image/upload/v1748331728/bronze-membership-icon-default-avatar-profile-icon-membership-icon-social-media-user-image-illustration-vector_ybxqsb.jpg',
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
});

const User = mongoose.model('User', userSchema);
module.exports = User;
