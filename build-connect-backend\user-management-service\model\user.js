const mongoose = require('mongoose');

const userSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
            trim: true,
        },
        email: {
            type: String,
            required: true,
            unique: true,
            trim: true,
        },
        phone: {
            type: String,
            required: true,
            unique: true,
            trim: true,
        },
        password: {
            type: String,
            required: true,
            minlength: 6,
        },
        role: {
            type: String,
            enum: ['admin', 'user', 'contractor', 'broker'],
            default: 'user',
        },
        location: String,
        documents: {
            type: [String],
            default: [],
        },
        specialties: {
            type: [String],
            default: [],
        },
        portfolio: {
            type: [String],
            default: [],
        },
        experience: {
            type: Number,
            default: 0,
        },
        verifiedByBroker: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
        },
        verificationStatus: {
            type: String,
            enum: ['pending', 'verified', 'rejected'],
            default: 'pending',
        },
        approvalDate: Date,
        ratings: {
            type: [Number],
            default: [],
        },
        totalProjects: {
            type: Number,
            default: 0,
        },
    },
    {
        timestamps: true,
    }
);

const User = mongoose.model('User', userSchema);
module.exports = User;
