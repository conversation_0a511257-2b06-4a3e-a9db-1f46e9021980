const mongoose = require('mongoose');

const assetSchema = new mongoose.Schema({
    // Basic asset information
    filename: {
        type: String,
        required: true,
        trim: true
    },
    originalName: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    
    // File details
    fileType: {
        type: String,
        required: true,
        enum: ['image', 'video', 'document', 'audio', 'other']
    },
    mimeType: {
        type: String,
        required: true
    },
    fileSize: {
        type: Number,
        required: true // Size in bytes
    },
    
    // Asset category and purpose
    category: {
        type: String,
        required: true,
        enum: [
            'profile_picture',
            'portfolio_item',
            'document',
            'project_file',
            'verification_document',
            'license_document',
            'certificate',
            'before_after_photo',
            'progress_photo',
            'other'
        ]
    },
    
    // Storage information
    cloudinaryUrl: {
        type: String,
        required: true
    },
    cloudinaryPublicId: {
        type: String,
        required: true,
        unique: true
    },
    secureUrl: {
        type: String,
        required: true
    },
    
    // Image/Video specific metadata
    dimensions: {
        width: {
            type: Number
        },
        height: {
            type: Number
        }
    },
    duration: {
        type: Number // For video/audio files in seconds
    },
    
    // Relationships
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    relatedTo: {
        entityType: {
            type: String,
            enum: ['user', 'project', 'service_request', 'booking', 'portfolio'],
            required: true
        },
        entityId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
        }
    },
    
    // Access control
    visibility: {
        type: String,
        enum: ['public', 'private', 'restricted'],
        default: 'private'
    },
    accessibleBy: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    
    // Asset status
    status: {
        type: String,
        enum: ['active', 'archived', 'deleted', 'processing'],
        default: 'active'
    },
    
    // Verification for documents
    isVerified: {
        type: Boolean,
        default: false
    },
    verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    verifiedAt: {
        type: Date
    },
    
    // Tags for better organization
    tags: [{
        type: String,
        trim: true
    }],
    
    // Metadata for additional information
    metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    
    // Expiry for temporary files
    expiresAt: {
        type: Date
    }
}, {
    timestamps: true
});

// Indexes for better performance
assetSchema.index({ uploadedBy: 1, category: 1 });
assetSchema.index({ 'relatedTo.entityType': 1, 'relatedTo.entityId': 1 });
assetSchema.index({ cloudinaryPublicId: 1 });
assetSchema.index({ status: 1 });
assetSchema.index({ createdAt: -1 });
assetSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for auto-deletion

// Virtual for file size in human readable format
assetSchema.virtual('fileSizeFormatted').get(function() {
    const bytes = this.fileSize;
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual for asset URL with transformations
assetSchema.virtual('thumbnailUrl').get(function() {
    if (this.fileType === 'image') {
        // Generate thumbnail URL for images
        return this.cloudinaryUrl.replace('/upload/', '/upload/w_300,h_300,c_fill/');
    }
    return this.cloudinaryUrl;
});

// Method to check if user has access to this asset
assetSchema.methods.hasAccess = function(userId) {
    // Owner always has access
    if (this.uploadedBy.toString() === userId.toString()) {
        return true;
    }
    
    // Public assets are accessible to all
    if (this.visibility === 'public') {
        return true;
    }
    
    // Check if user is in accessibleBy list
    if (this.visibility === 'restricted') {
        return this.accessibleBy.some(id => id.toString() === userId.toString());
    }
    
    return false;
};

// Static method to get assets by category
assetSchema.statics.getByCategory = function(category, userId, options = {}) {
    const query = {
        category,
        status: 'active',
        $or: [
            { uploadedBy: userId },
            { visibility: 'public' },
            { visibility: 'restricted', accessibleBy: userId }
        ]
    };
    
    return this.find(query, null, options);
};

// Static method to get user's assets
assetSchema.statics.getUserAssets = function(userId, options = {}) {
    const query = {
        uploadedBy: userId,
        status: 'active'
    };
    
    return this.find(query, null, options);
};

// Pre-save middleware to set default values
assetSchema.pre('save', function(next) {
    // Set secure URL if not provided
    if (!this.secureUrl && this.cloudinaryUrl) {
        this.secureUrl = this.cloudinaryUrl.replace('http://', 'https://');
    }
    
    next();
});

const Asset = mongoose.model('Asset', assetSchema);

module.exports = Asset;
