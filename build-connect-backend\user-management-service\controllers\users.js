const bcrypt = require('bcrypt');
const {
    createJSONToken,
    sessionID,
    base64Encode,
    getDecodedToken,
} = require('@build-connect/utils');
const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../model/user');
const Asset = require('../model/asset');
const Site = require('../model/site');

const { client } = require('../cache');
const { JWT_REFRESH_KEY, JWT_ACCESS_KEY } = require('../config').getAll();

exports.signup = async (req, res) => {
    const { email, password, phone, name } = req.body;
    const hashedPw = await bcrypt.hash(password, 10);

    let avatarUrl;
    if (req.file && req.file.path) {
        avatarUrl = req.file.path;
    }

    const user = new User({
        email,
        password: hashedPw,
        phone,
        name,
        avatar: avatarUrl,
    });

    try {
        await user.save();
    } catch (error) {
        if (
            error.code === 11000 &&
            error.keyPattern &&
            error.keyPattern.email
        ) {
            throw new ExpressError('user already exists', 409);
        }
        throw error;
    }

    res.status(200).json({
        message: 'Successfully signed up!',
    });
};

exports.login = async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (!user) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const refreshToken = createJSONToken(
        { userId: user._id.toString() },
        JWT_REFRESH_KEY,
        '720h'
    );

    const id = sessionID();

    const idToken = {
        id: user._id.toString(),
        email: user.email,
        username: user.username,
        role: user.role,
    };

    const tokenResponse = {
        accessToken,
        refreshToken,
        idToken: base64Encode(JSON.stringify(idToken)),
    };

    await client.set(id, JSON.stringify(tokenResponse), { EX: 720 * 3600 });

    res.status(200).json({
        message: 'logged in sucessfully',
        accessToken,
        sessionID: id,
    });
};

exports.refreshToken = async (req, res) => {
    const id = req.get('Session');

    if (!id) {
        res.status(401).json({ message: 'unauthorized' });
    }

    const tokenResponse = await client.get(sessionID);

    if (!tokenResponse) {
        res.status(401).json({ message: 'unauthorized' });
    }

    getDecodedToken(tokenResponse.refreshToken);

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const token = {
        ...tokenResponse,
        accessToken,
    };

    await client.set(sessionID, JSON.stringify(token), {
        KEEPTTL: true,
    });

    res.status(200).json({ accessToken });
};

// Site Management Functions

// Create a new site
exports.createSite = async (req, res) => {
    try {
        const {
            name,
            description,
            siteType,
            address,
            size,
            projectType,
            timeline,
            budget,
            broker,
            requirements,
            settings,
            tags,
            notes
        } = req.body;

        // Validate required fields
        if (!name || !siteType || !address || !size || !projectType || !timeline || !budget) {
            throw new ExpressError('Missing required fields', 400);
        }

        // Validate timeline
        if (new Date(timeline.startDate) >= new Date(timeline.endDate)) {
            throw new ExpressError('End date must be after start date', 400);
        }

        // Create new site
        const site = new Site({
            name,
            description,
            siteType,
            address,
            size,
            projectType,
            timeline,
            budget,
            owner: req.user.id,
            broker,
            requirements: requirements || {},
            settings: settings || {},
            tags: tags || [],
            notes
        });

        await site.save();

        // Invalidate cache
        const cacheKey = `sites:user:${req.user.id}`;
        await client.del(cacheKey);

        res.status(201).json({
            message: 'Site created successfully',
            site: {
                id: site._id,
                name: site.name,
                siteType: site.siteType,
                projectType: site.projectType,
                status: site.status,
                fullAddress: site.fullAddress,
                timeline: site.timeline,
                budget: site.budget,
                createdAt: site.createdAt
            }
        });
    } catch (error) {
        throw new ExpressError(`Site creation failed: ${error.message}`, 500);
    }
};

// Get user's sites
exports.getUserSites = async (req, res) => {
    try {
        const {
            status,
            siteType,
            projectType,
            page = 1,
            limit = 20,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        const cacheKey = `sites:user:${req.user.id}:${status || 'all'}:${page}:${limit}`;
        const cachedSites = await client.get(cacheKey);

        if (cachedSites) {
            return res.json(JSON.parse(cachedSites));
        }

        // Build query based on user role
        let query = { isArchived: false };

        if (req.user.role === 'admin') {
            // Admin can see all sites
        } else if (req.user.role === 'broker') {
            // Broker can see sites they're assigned to or own
            query.$or = [
                { owner: req.user.id },
                { broker: req.user.id }
            ];
        } else if (req.user.role === 'contractor') {
            // Contractor can see sites they're assigned to or own
            query.$or = [
                { owner: req.user.id },
                { 'contractors.contractor': req.user.id, 'contractors.status': { $in: ['assigned', 'active'] } }
            ];
        } else {
            // Regular users can only see their own sites
            query.owner = req.user.id;
        }

        // Add filters
        if (status) query.status = status;
        if (siteType) query.siteType = siteType;
        if (projectType) query.projectType = projectType;

        // Pagination options
        const options = {
            limit: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit),
            sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
        };

        const sites = await Site.find(query, null, options)
            .populate('owner', 'name email')
            .populate('broker', 'name email brokerCompany')
            .populate('contractors.contractor', 'name email');

        const totalSites = await Site.countDocuments(query);

        const response = {
            sites: sites.map(site => ({
                id: site._id,
                name: site.name,
                description: site.description,
                siteType: site.siteType,
                projectType: site.projectType,
                status: site.status,
                fullAddress: site.fullAddress,
                size: site.size,
                timeline: site.timeline,
                budget: site.budget,
                progress: site.progress,
                owner: site.owner,
                broker: site.broker,
                contractors: site.contractors,
                createdAt: site.createdAt,
                updatedAt: site.updatedAt
            })),
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalSites / parseInt(limit)),
                totalSites,
                hasNext: parseInt(page) < Math.ceil(totalSites / parseInt(limit)),
                hasPrev: parseInt(page) > 1
            }
        };

        // Cache for 15 minutes
        await client.setEx(cacheKey, 900, JSON.stringify(response));
        res.json(response);
    } catch (error) {
        throw new ExpressError(`Failed to fetch sites: ${error.message}`, 500);
    }
};

// Get site by ID
exports.getSiteById = async (req, res) => {
    try {
        const { siteId } = req.params;

        const site = await Site.findById(siteId)
            .populate('owner', 'name email phone')
            .populate('broker', 'name email phone brokerCompany brokerRegion')
            .populate('contractors.contractor', 'name email phone specialties averageRating')
            .populate('updates.author', 'name email')
            .populate('issues.reportedBy', 'name email')
            .populate('issues.assignedTo', 'name email');

        if (!site || site.isArchived) {
            throw new ExpressError('Site not found', 404);
        }

        // Check access permissions
        if (!site.hasAccess(req.user.id) && req.user.role !== 'admin') {
            throw new ExpressError('Access denied', 403);
        }

        res.json({
            id: site._id,
            name: site.name,
            description: site.description,
            siteType: site.siteType,
            projectType: site.projectType,
            address: site.address,
            fullAddress: site.fullAddress,
            size: site.size,
            timeline: site.timeline,
            actualDuration: site.actualDuration,
            budget: site.budget,
            budgetVariance: site.budgetVariance,
            budgetVariancePercentage: site.budgetVariancePercentage,
            owner: site.owner,
            broker: site.broker,
            contractors: site.contractors,
            status: site.status,
            progress: site.progress,
            requirements: site.requirements,
            assets: site.assets,
            updates: site.updates,
            issues: site.issues,
            settings: site.settings,
            tags: site.tags,
            notes: site.notes,
            createdAt: site.createdAt,
            updatedAt: site.updatedAt
        });
    } catch (error) {
        throw new ExpressError(`Failed to fetch site: ${error.message}`, 500);
    }
};

// Update site
exports.updateSite = async (req, res) => {
    try {
        const { siteId } = req.params;
        const {
            name,
            description,
            timeline,
            budget,
            broker,
            requirements,
            settings,
            tags,
            notes
        } = req.body;

        const site = await Site.findById(siteId);
        if (!site || site.isArchived) {
            throw new ExpressError('Site not found', 404);
        }

        // Check permissions (owner, broker, or admin can update)
        const canUpdate = site.owner.toString() === req.user.id ||
            (site.broker && site.broker.toString() === req.user.id) ||
            req.user.role === 'admin';

        if (!canUpdate) {
            throw new ExpressError('Access denied', 403);
        }

        // Update allowed fields
        if (name !== undefined) site.name = name;
        if (description !== undefined) site.description = description;
        if (timeline !== undefined) {
            // Validate timeline if provided
            if (timeline.startDate && timeline.endDate &&
                new Date(timeline.startDate) >= new Date(timeline.endDate)) {
                throw new ExpressError('End date must be after start date', 400);
            }
            site.timeline = { ...site.timeline, ...timeline };
        }
        if (budget !== undefined) site.budget = { ...site.budget, ...budget };
        if (broker !== undefined) site.broker = broker;
        if (requirements !== undefined) site.requirements = { ...site.requirements, ...requirements };
        if (settings !== undefined) site.settings = { ...site.settings, ...settings };
        if (tags !== undefined) site.tags = tags;
        if (notes !== undefined) site.notes = notes;

        await site.save();

        // Invalidate cache
        const cacheKey = `sites:user:${req.user.id}`;
        await client.del(cacheKey);

        res.json({
            message: 'Site updated successfully',
            site: {
                id: site._id,
                name: site.name,
                description: site.description,
                timeline: site.timeline,
                budget: site.budget,
                updatedAt: site.updatedAt
            }
        });
    } catch (error) {
        throw new ExpressError(`Update failed: ${error.message}`, 500);
    }
};

// Add contractor to site
exports.addContractorToSite = async (req, res) => {
    try {
        const { siteId } = req.params;
        const { contractorId, role = 'secondary' } = req.body;

        const site = await Site.findById(siteId);
        if (!site || site.isArchived) {
            throw new ExpressError('Site not found', 404);
        }

        // Check permissions (owner, broker, or admin can add contractors)
        const canManage = site.owner.toString() === req.user.id ||
            (site.broker && site.broker.toString() === req.user.id) ||
            req.user.role === 'admin';

        if (!canManage) {
            throw new ExpressError('Access denied', 403);
        }

        // Verify contractor exists and has contractor role
        const contractor = await User.findById(contractorId);
        if (!contractor || contractor.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        // Add contractor to site
        site.addContractor(contractorId, role);
        await site.save();

        // Invalidate cache
        const cacheKey = `sites:user:${req.user.id}`;
        await client.del(cacheKey);

        res.json({
            message: 'Contractor added to site successfully',
            contractor: {
                id: contractor._id,
                name: contractor.name,
                email: contractor.email,
                role: role,
                status: 'assigned'
            }
        });
    } catch (error) {
        throw new ExpressError(`Failed to add contractor: ${error.message}`, 500);
    }
};

// Update site progress
exports.updateSiteProgress = async (req, res) => {
    try {
        const { siteId } = req.params;
        const { percentage, milestoneUpdate, updateMessage } = req.body;

        const site = await Site.findById(siteId);
        if (!site || site.isArchived) {
            throw new ExpressError('Site not found', 404);
        }

        // Check permissions (owner, broker, assigned contractors, or admin can update progress)
        const canUpdate = site.hasAccess(req.user.id) || req.user.role === 'admin';

        if (!canUpdate) {
            throw new ExpressError('Access denied', 403);
        }

        // Update progress
        if (percentage !== undefined) {
            site.updateProgress(percentage, milestoneUpdate);
        }

        // Add update message if provided
        if (updateMessage) {
            site.updates.push({
                author: req.user.id,
                message: updateMessage,
                type: 'progress'
            });
        }

        await site.save();

        // Invalidate cache
        const cacheKey = `sites:user:${req.user.id}`;
        await client.del(cacheKey);

        res.json({
            message: 'Site progress updated successfully',
            progress: {
                percentage: site.progress.percentage,
                status: site.status,
                lastUpdated: site.progress.lastUpdated
            }
        });
    } catch (error) {
        throw new ExpressError(`Failed to update progress: ${error.message}`, 500);
    }
};

// Add site update/comment
exports.addSiteUpdate = async (req, res) => {
    try {
        const { siteId } = req.params;
        const { message, type = 'general', attachments } = req.body;

        if (!message) {
            throw new ExpressError('Message is required', 400);
        }

        const site = await Site.findById(siteId);
        if (!site || site.isArchived) {
            throw new ExpressError('Site not found', 404);
        }

        // Check permissions
        const canUpdate = site.hasAccess(req.user.id) || req.user.role === 'admin';

        if (!canUpdate) {
            throw new ExpressError('Access denied', 403);
        }

        // Add update
        site.updates.push({
            author: req.user.id,
            message,
            type,
            attachments: attachments || []
        });

        await site.save();

        // Get the newly added update with populated author
        const updatedSite = await Site.findById(siteId)
            .populate('updates.author', 'name email');

        const newUpdate = updatedSite.updates[updatedSite.updates.length - 1];

        res.json({
            message: 'Update added successfully',
            update: {
                id: newUpdate._id,
                author: newUpdate.author,
                message: newUpdate.message,
                type: newUpdate.type,
                attachments: newUpdate.attachments,
                createdAt: newUpdate.createdAt
            }
        });
    } catch (error) {
        throw new ExpressError(`Failed to add update: ${error.message}`, 500);
    }
};
